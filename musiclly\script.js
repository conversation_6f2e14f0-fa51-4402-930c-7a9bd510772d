class MusicStreamingApp {
  constructor() {
    // Application state
    this.currentGenre = "default"
    this.isPlaying = false
    this.currentTrackIndex = 0
    this.currentTime = 154 // 2:34 in seconds
    this.totalTime = 355 // 5:55 in seconds
    this.volume = 70
    this.progressInterval = null

    // DOM elements
    this.elements = {}

    // Sample playlist data
    this.playlists = {
      default: [
        { title: "Bohemian Rhapsody", artist: "Queen", duration: "5:55", icon: "fas fa-music" },
        { title: "Hotel California", artist: "Eagles", duration: "6:30", icon: "fas fa-guitar" },
        { title: "Imagine", artist: "<PERSON>", duration: "3:07", icon: "fas fa-dove" },
        { title: "Stairway to Heaven", artist: "Led Zeppelin", duration: "8:02", icon: "fas fa-stairs" },
        { title: "Sweet Child O' Mine", artist: "Guns N' Roses", duration: "5:03", icon: "fas fa-heart" },
      ],
      rock: [
        { title: "<PERSON><PERSON>ru<PERSON>", artist: "AC/DC", duration: "4:52", icon: "fas fa-bolt" },
        { title: "Enter Sandman", artist: "Metallica", duration: "5:31", icon: "fas fa-moon" },
        { title: "Smoke on the Water", artist: "Deep Purple", duration: "5:40", icon: "fas fa-fire" },
        { title: "Back in Black", artist: "AC/DC", duration: "4:15", icon: "fas fa-skull" },
        { title: "Paradise City", artist: "Guns N' Roses", duration: "6:46", icon: "fas fa-city" },
      ],
      pop: [
        { title: "Blinding Lights", artist: "The Weeknd", duration: "3:20", icon: "fas fa-star" },
        { title: "Shape of You", artist: "Ed Sheeran", duration: "3:53", icon: "fas fa-heart" },
        { title: "Uptown Funk", artist: "Bruno Mars", duration: "4:30", icon: "fas fa-fire" },
        { title: "Can't Stop the Feeling", artist: "Justin Timberlake", duration: "3:56", icon: "fas fa-smile" },
        { title: "Shake It Off", artist: "Taylor Swift", duration: "3:39", icon: "fas fa-dance" },
      ],
      jazz: [
        { title: "Take Five", artist: "Dave Brubeck", duration: "5:24", icon: "fas fa-music" },
        { title: "So What", artist: "Miles Davis", duration: "9:22", icon: "fas fa-trumpet" },
        { title: "All Blues", artist: "Miles Davis", duration: "11:33", icon: "fas fa-music" },
        { title: "Blue Train", artist: "John Coltrane", duration: "10:42", icon: "fas fa-train" },
        { title: "Autumn Leaves", artist: "Bill Evans", duration: "6:55", icon: "fas fa-leaf" },
      ],
      electronic: [
        { title: "Strobe", artist: "Deadmau5", duration: "10:36", icon: "fas fa-bolt" },
        { title: "Levels", artist: "Avicii", duration: "6:17", icon: "fas fa-chart-line" },
        { title: "Titanium", artist: "David Guetta", duration: "4:05", icon: "fas fa-gem" },
        { title: "Animals", artist: "Martin Garrix", duration: "5:05", icon: "fas fa-paw" },
        { title: "Clarity", artist: "Zedd", duration: "4:31", icon: "fas fa-eye" },
      ],
      classical: [
        { title: "Symphony No. 9", artist: "Beethoven", duration: "67:00", icon: "fas fa-crown" },
        { title: "The Four Seasons", artist: "Vivaldi", duration: "43:00", icon: "fas fa-leaf" },
        { title: "Canon in D", artist: "Pachelbel", duration: "5:30", icon: "fas fa-church" },
        { title: "Ave Maria", artist: "Schubert", duration: "6:52", icon: "fas fa-dove" },
        { title: "Moonlight Sonata", artist: "Beethoven", duration: "15:30", icon: "fas fa-moon" },
      ],
    }

    // Initialize the application
    this.init()
  }

  // Initialize the application
  init() {
    try {
      this.showLoading()
      this.cacheElements()
      this.bindEvents()
      this.updatePlaylist()
      this.updateCurrentTrack()
      this.startProgressUpdates()
      this.hideLoading()
      console.log("Music Streaming App initialized successfully")
    } catch (error) {
      this.handleError("Failed to initialize application", error)
    }
  }

  // Cache DOM elements
  cacheElements() {
    const elementIds = [
      "themeToggle",
      "genreButtons",
      "albumArt",
      "trackTitle",
      "trackArtist",
      "currentTime",
      "totalTime",
      "progressBar",
      "progressFill",
      "playBtn",
      "playIcon",
      "prevBtn",
      "nextBtn",
      "rewindBtn",
      "forwardBtn",
      "volumeSlider",
      "playlistContainer",
      "loadingIndicator",
      "errorMessage",
      "closeError",
      "errorText",
    ]

    elementIds.forEach((id) => {
      this.elements[id] = document.getElementById(id)
      if (!this.elements[id]) {
        console.warn(`Element with ID '${id}' not found`)
      }
    })
  }

  // Bind event listeners
  bindEvents() {
    // Theme toggle
    if (this.elements.themeToggle) {
      this.elements.themeToggle.addEventListener("click", () => this.toggleTheme())
    }

    // Genre buttons
    if (this.elements.genreButtons) {
      this.elements.genreButtons.addEventListener("click", (e) => {
        if (e.target.classList.contains("genre-btn")) {
          const genre = e.target.dataset.genre
          if (genre) this.setGenre(genre)
        }
      })
    }

    // Playback controls
    if (this.elements.playBtn) {
      this.elements.playBtn.addEventListener("click", () => this.togglePlay())
    }
    if (this.elements.prevBtn) {
      this.elements.prevBtn.addEventListener("click", () => this.previousTrack())
    }
    if (this.elements.nextBtn) {
      this.elements.nextBtn.addEventListener("click", () => this.nextTrack())
    }
    if (this.elements.rewindBtn) {
      this.elements.rewindBtn.addEventListener("click", () => this.rewind())
    }
    if (this.elements.forwardBtn) {
      this.elements.forwardBtn.addEventListener("click", () => this.fastForward())
    }

    // Progress bar
    if (this.elements.progressBar) {
      this.elements.progressBar.addEventListener("click", (e) => this.seekTo(e))
      this.elements.progressBar.addEventListener("keydown", (e) => this.handleProgressKeydown(e))
    }

    // Volume control
    if (this.elements.volumeSlider) {
      this.elements.volumeSlider.addEventListener("input", (e) => this.setVolume(e.target.value))
    }

    // Error message close
    if (this.elements.closeError) {
      this.elements.closeError.addEventListener("click", () => this.hideError())
    }

    // Keyboard shortcuts
    document.addEventListener("keydown", (e) => this.handleKeyboardShortcuts(e))

    // Playlist delegation
    if (this.elements.playlistContainer) {
      this.elements.playlistContainer.addEventListener("click", (e) => {
        const trackItem = e.target.closest(".track-item")
        if (trackItem) {
          const index = Number.parseInt(trackItem.dataset.index)
          if (!isNaN(index)) this.playTrack(index)
        }
      })
    }
  }

  // Handle keyboard shortcuts
  handleKeyboardShortcuts(e) {
    // Prevent shortcuts when typing in inputs
    if (e.target.tagName === "INPUT") return

    switch (e.code) {
      case "Space":
        e.preventDefault()
        this.togglePlay()
        break
      case "ArrowLeft":
        e.preventDefault()
        this.rewind()
        break
      case "ArrowRight":
        e.preventDefault()
        this.fastForward()
        break
      case "ArrowUp":
        e.preventDefault()
        this.setVolume(Math.min(100, this.volume + 5))
        break
      case "ArrowDown":
        e.preventDefault()
        this.setVolume(Math.max(0, this.volume - 5))
        break
    }
  }

  // Handle progress bar keyboard navigation
  handleProgressKeydown(e) {
    const step = 5 // 5 seconds
    switch (e.code) {
      case "ArrowLeft":
        e.preventDefault()
        this.currentTime = Math.max(0, this.currentTime - step)
        break
      case "ArrowRight":
        e.preventDefault()
        this.currentTime = Math.min(this.totalTime, this.currentTime + step)
        break
      case "Home":
        e.preventDefault()
        this.currentTime = 0
        break
      case "End":
        e.preventDefault()
        this.currentTime = this.totalTime
        break
    }
  }

  // Set genre and update theme
  setGenre(genre) {
    try {
      this.currentGenre = genre

      // Update active genre button
      const genreButtons = document.querySelectorAll(".genre-btn")
      genreButtons.forEach((btn) => {
        const isActive = btn.dataset.genre === genre
        btn.classList.toggle("is-active", isActive)
        btn.setAttribute("aria-pressed", isActive.toString())
      })

      // Apply theme
      document.body.className = genre === "default" ? "" : `theme-${genre}`

      // Update playlist
      this.updatePlaylist()

      // Update current track to first track of new genre
      if (this.playlists[genre] && this.playlists[genre].length > 0) {
        this.currentTrackIndex = 0
        this.updateCurrentTrack()
      }

      console.log(`Genre changed to: ${genre}`)
    } catch (error) {
      this.handleError("Failed to set genre", error)
    }
  }

  // Toggle between themes
  toggleTheme() {
    const themes = ["default", "rock", "pop", "jazz", "electronic", "classical"]
    const currentIndex = themes.indexOf(this.currentGenre)
    const nextIndex = (currentIndex + 1) % themes.length
    this.setGenre(themes[nextIndex])
  }

  // Update playlist display
  updatePlaylist() {
    try {
      if (!this.elements.playlistContainer) return

      const tracks = this.playlists[this.currentGenre] || []

      this.elements.playlistContainer.innerHTML = tracks
        .map(
          (track, index) => `
                <div class="track-item ${index === this.currentTrackIndex ? "is-playing" : ""}" 
                     data-index="${index}" 
                     role="listitem" 
                     tabindex="0"
                     aria-label="Track ${index + 1}: ${track.title} by ${track.artist}">
                    <div class="track-number">
                        ${
                          index === this.currentTrackIndex && this.isPlaying
                            ? '<i class="fas fa-play" aria-hidden="true"></i>'
                            : index + 1
                        }
                    </div>
                    <div class="track-info">
                        <div class="track-title">${this.escapeHtml(track.title)}</div>
                        <div class="track-artist">${this.escapeHtml(track.artist)}</div>
                    </div>
                    <div class="track-duration">${track.duration}</div>
                </div>
            `,
        )
        .join("")
    } catch (error) {
      this.handleError("Failed to update playlist", error)
    }
  }

  // Play specific track
  playTrack(index) {
    try {
      const tracks = this.playlists[this.currentGenre] || []
      if (index < 0 || index >= tracks.length) return

      this.currentTrackIndex = index
      this.isPlaying = true
      this.updateCurrentTrack()
      this.updatePlaylist()
      this.updatePlayButton()

      console.log(`Playing track ${index + 1}: ${tracks[index].title}`)
    } catch (error) {
      this.handleError("Failed to play track", error)
    }
  }

  // Update current track display
  updateCurrentTrack() {
    try {
      const tracks = this.playlists[this.currentGenre] || []
      if (!tracks[this.currentTrackIndex]) return

      const track = tracks[this.currentTrackIndex]

      if (this.elements.trackTitle) {
        this.elements.trackTitle.textContent = track.title
      }
      if (this.elements.trackArtist) {
        this.elements.trackArtist.textContent = track.artist
      }
      if (this.elements.albumArt) {
        this.elements.albumArt.innerHTML = `<i class="${track.icon}" aria-hidden="true"></i>`
        this.elements.albumArt.setAttribute("aria-label", `Album artwork for ${track.title}`)
      }

      // Reset progress for new track
      this.currentTime = 0
      this.totalTime = this.parseDuration(track.duration)

      console.log(`Current track updated: ${track.title}`)
    } catch (error) {
      this.handleError("Failed to update current track", error)
    }
  }

  // Parse duration string to seconds
  parseDuration(duration) {
    try {
      const parts = duration.split(":")
      if (parts.length === 2) {
        return Number.parseInt(parts[0]) * 60 + Number.parseInt(parts[1])
      } else if (parts.length === 3) {
        return Number.parseInt(parts[0]) * 3600 + Number.parseInt(parts[1]) * 60 + Number.parseInt(parts[2])
      }
      return 0
    } catch (error) {
      console.warn("Failed to parse duration:", duration)
      return 0
    }
  }

  // Format seconds to MM:SS or HH:MM:SS
  formatTime(seconds) {
    try {
      const hours = Math.floor(seconds / 3600)
      const mins = Math.floor((seconds % 3600) / 60)
      const secs = seconds % 60

      if (hours > 0) {
        return `${hours}:${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
      } else {
        return `${mins}:${secs.toString().padStart(2, "0")}`
      }
    } catch (error) {
      return "0:00"
    }
  }

  // Toggle play/pause
  togglePlay() {
    try {
      this.isPlaying = !this.isPlaying
      this.updatePlayButton()
      this.updatePlaylist()

      console.log(this.isPlaying ? "Playing" : "Paused")
    } catch (error) {
      this.handleError("Failed to toggle play", error)
    }
  }

  // Update play button
  updatePlayButton() {
    try {
      if (!this.elements.playIcon || !this.elements.playBtn) return

      this.elements.playIcon.className = this.isPlaying ? "fas fa-pause" : "fas fa-play"
      this.elements.playBtn.setAttribute("aria-label", this.isPlaying ? "Pause" : "Play")
    } catch (error) {
      console.warn("Failed to update play button")
    }
  }

  // Previous track
  previousTrack() {
    try {
      const tracks = this.playlists[this.currentGenre] || []
      this.currentTrackIndex = this.currentTrackIndex > 0 ? this.currentTrackIndex - 1 : tracks.length - 1
      this.updateCurrentTrack()
      this.updatePlaylist()

      console.log("Previous track")
    } catch (error) {
      this.handleError("Failed to go to previous track", error)
    }
  }

  // Next track
  nextTrack() {
    try {
      const tracks = this.playlists[this.currentGenre] || []
      this.currentTrackIndex = (this.currentTrackIndex + 1) % tracks.length
      this.updateCurrentTrack()
      this.updatePlaylist()

      console.log("Next track")
    } catch (error) {
      this.handleError("Failed to go to next track", error)
    }
  }

  // Rewind
  rewind() {
    try {
      this.currentTime = Math.max(0, this.currentTime - 10)
      console.log("Rewound 10 seconds")
    } catch (error) {
      this.handleError("Failed to rewind", error)
    }
  }

  // Fast forward
  fastForward() {
    try {
      this.currentTime = Math.min(this.totalTime, this.currentTime + 10)
      console.log("Fast forwarded 10 seconds")
    } catch (error) {
      this.handleError("Failed to fast forward", error)
    }
  }

  // Seek to position
  seekTo(event) {
    try {
      const progressBar = event.currentTarget
      const rect = progressBar.getBoundingClientRect()
      const percent = (event.clientX - rect.left) / rect.width
      this.currentTime = Math.floor(this.totalTime * percent)

      console.log(`Seeked to ${this.formatTime(this.currentTime)}`)
    } catch (error) {
      this.handleError("Failed to seek", error)
    }
  }

  // Set volume
  setVolume(value) {
    try {
      this.volume = Math.max(0, Math.min(100, Number.parseInt(value)))
      if (this.elements.volumeSlider) {
        this.elements.volumeSlider.value = this.volume
      }

      console.log(`Volume set to ${this.volume}%`)
    } catch (error) {
      this.handleError("Failed to set volume", error)
    }
  }

  // Start progress updates
  startProgressUpdates() {
    if (this.progressInterval) {
      clearInterval(this.progressInterval)
    }

    this.progressInterval = setInterval(() => {
      this.updateProgress()
    }, 1000)
  }

  // Update progress bar and time
  updateProgress() {
    try {
      if (this.isPlaying && this.currentTime < this.totalTime) {
        this.currentTime++
      }

      const percent = this.totalTime > 0 ? (this.currentTime / this.totalTime) * 100 : 0

      if (this.elements.progressFill) {
        this.elements.progressFill.style.width = `${percent}%`
      }
      if (this.elements.progressBar) {
        this.elements.progressBar.setAttribute("aria-valuenow", Math.round(percent))
      }
      if (this.elements.currentTime) {
        this.elements.currentTime.textContent = this.formatTime(this.currentTime)
      }
      if (this.elements.totalTime) {
        this.elements.totalTime.textContent = this.formatTime(this.totalTime)
      }

      // Auto-advance to next track
      if (this.currentTime >= this.totalTime && this.isPlaying) {
        this.nextTrack()
      }
    } catch (error) {
      console.warn("Failed to update progress")
    }
  }

  // Show loading indicator
  showLoading() {
    if (this.elements.loadingIndicator) {
      this.elements.loadingIndicator.classList.add("show")
      this.elements.loadingIndicator.setAttribute("aria-hidden", "false")
    }
  }

  // Hide loading indicator
  hideLoading() {
    if (this.elements.loadingIndicator) {
      this.elements.loadingIndicator.classList.remove("show")
      this.elements.loadingIndicator.setAttribute("aria-hidden", "true")
    }
  }

  // Show error message
  showError(message) {
    if (this.elements.errorMessage && this.elements.errorText) {
      this.elements.errorText.textContent = message
      this.elements.errorMessage.classList.add("show")
      this.elements.errorMessage.setAttribute("aria-hidden", "false")

      // Auto-hide after 5 seconds
      setTimeout(() => this.hideError(), 5000)
    }
  }

  // Hide error message
  hideError() {
    if (this.elements.errorMessage) {
      this.elements.errorMessage.classList.remove("show")
      this.elements.errorMessage.setAttribute("aria-hidden", "true")
    }
  }

  // Handle errors
  handleError(message, error = null) {
    console.error(message, error)
    this.showError(message)
    this.hideLoading()
  }

  // Escape HTML to prevent XSS
  escapeHtml(text) {
    const div = document.createElement("div")
    div.textContent = text
    return div.innerHTML
  }

  // Cleanup
  destroy() {
    if (this.progressInterval) {
      clearInterval(this.progressInterval)
    }
    console.log("Music Streaming App destroyed")
  }
}

// Initialize the application when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  try {
    window.musicApp = new MusicStreamingApp()
  } catch (error) {
    console.error("Failed to initialize Music Streaming App:", error)

    // Show basic error message if app fails to load
    const errorDiv = document.createElement("div")
    errorDiv.innerHTML = `
            <div style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); 
                        background: #ff3860; color: white; padding: 2rem; border-radius: 10px; 
                        text-align: center; z-index: 9999;">
                <h2>Application Error</h2>
                <p>Failed to load the music streaming application.</p>
                <button onclick="location.reload()" style="background: white; color: #ff3860; 
                        border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">
                    Reload Page
                </button>
            </div>
        `
    document.body.appendChild(errorDiv)
  }
})

// Handle page unload
window.addEventListener("beforeunload", () => {
  if (window.musicApp) {
    window.musicApp.destroy()
  }
})
