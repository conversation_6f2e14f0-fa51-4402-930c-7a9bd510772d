:root {
  --primary-color: #3273dc;
  --secondary-color: #209cee;
  --accent-color: #ff3860;
  --background-color: #f5f5f5;
  --surface-color: #ffffff;
  --text-color: #363636;
  --text-light: #4a4a4a;
  --shadow-light: rgba(0, 0, 0, 0.1);
  --shadow-medium: rgba(0, 0, 0, 0.2);
  --shadow-heavy: rgba(0, 0, 0, 0.3);
}

/* Base Styles */
* {
  box-sizing: border-box;
}

*,
*::before,
*::after {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
  background: linear-gradient(135deg, var(--background-color) 0%, var(--secondary-color) 100%);
  color: var(--text-color);
  min-height: 100vh;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  margin: 0;
  padding: 0;
  line-height: 1.6;
}

/* App Container */
.app-container {
  background: var(--surface-color);
  border-radius: 15px;
  box-shadow: 0 10px 30px var(--shadow-light);
  overflow: hidden;
  margin: 20px;
  min-height: calc(100vh - 40px);
  position: relative;
}

/* Header Styles */
.header {
  background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
  color: white;
  padding: 1.5rem;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header .title {
  margin-bottom: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Theme Toggle */
.theme-toggle {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  border-radius: 25px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.theme-toggle:hover,
.theme-toggle:focus {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}

/* Genre Selector */
.genre-selector {
  padding: 1.5rem;
  background: var(--surface-color);
  border-bottom: 1px solid #e8e8e8;
}

.genre-btn {
  margin: 0.25rem;
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
  background: transparent;
  transition: all 0.3s ease;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.genre-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--primary-color);
  transition: left 0.3s ease;
  z-index: -1;
}

.genre-btn:hover::before,
.genre-btn.is-active::before {
  left: 0;
}

.genre-btn:hover,
.genre-btn.is-active,
.genre-btn:focus {
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px var(--shadow-medium);
  outline: none;
}

/* Player Section */
.player-section {
  background: var(--surface-color);
  padding: 2rem;
  border-radius: 15px;
  margin: 1rem;
  box-shadow: 0 5px 20px var(--shadow-light);
}

/* Now Playing */
.now-playing {
  text-align: center;
  margin-bottom: 2rem;
}

.album-art {
  width: 200px;
  height: 200px;
  border-radius: 15px;
  margin: 0 auto 1rem;
  background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 4rem;
  color: white;
  box-shadow: 0 10px 30px var(--shadow-medium);
  position: relative;
  overflow: hidden;
}

.album-art::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* Progress Container */
.progress-container {
  margin: 1.5rem 0;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  position: relative;
  transition: height 0.2s ease;
}

.progress-bar:hover {
  height: 12px;
}

.progress-bar:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  width: 43%;
  transition: width 0.1s ease;
  border-radius: 4px;
}

/* Controls */
.controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin: 2rem 0;
  flex-wrap: wrap;
}

.control-btn {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: none;
  background: var(--primary-color);
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.control-btn::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transition: all 0.3s ease;
  transform: translate(-50%, -50%);
}

.control-btn:hover::before,
.control-btn:focus::before {
  width: 100%;
  height: 100%;
}

.control-btn:hover,
.control-btn:focus {
  transform: scale(1.1);
  box-shadow: 0 5px 20px var(--shadow-heavy);
  outline: none;
}

.control-btn:active {
  transform: scale(0.95);
}

.play-btn {
  width: 80px;
  height: 80px;
  font-size: 2rem;
  background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
}

/* Volume Control */
.volume-control {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 1rem;
}

.volume-slider {
  flex: 1;
  height: 6px;
  background: #e8e8e8;
  border-radius: 3px;
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--accent-color);
  cursor: pointer;
  transition: all 0.3s ease;
}

.volume-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 0 10px var(--accent-color);
}

.volume-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--accent-color);
  cursor: pointer;
  border: none;
  transition: all 0.3s ease;
}

/* Playlist */
.playlist {
  background: var(--surface-color);
  margin: 1rem;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 20px var(--shadow-light);
}

.playlist-header {
  background: var(--primary-color);
  color: white;
  padding: 1rem;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.track-item {
  padding: 1rem;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
}

.track-item::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 0;
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), transparent);
  transition: width 0.3s ease;
}

.track-item:hover::before {
  width: 4px;
}

.track-item:hover {
  background: var(--background-color);
  transform: translateX(5px);
}

.track-item.is-playing {
  background: linear-gradient(90deg, rgba(var(--primary-color), 0.1), transparent);
  border-left: 4px solid var(--accent-color);
}

.track-item.is-playing::before {
  width: 4px;
  background: var(--accent-color);
}

.track-number {
  width: 30px;
  text-align: center;
  color: var(--text-light);
  font-weight: 500;
}

.track-info {
  flex: 1;
}

.track-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--text-color);
}

.track-artist {
  color: var(--text-light);
  font-size: 0.9rem;
}

.track-duration {
  color: var(--text-light);
  font-family: "Courier New", monospace;
  font-size: 0.9rem;
}

/* Loading and Error States */
.loading-indicator {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.loading-indicator.show {
  opacity: 1;
  visibility: visible;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.error-message {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1001;
  max-width: 400px;
  opacity: 0;
  visibility: hidden;
  transform: translateX(100%);
  transition: all 0.3s ease;
}

.error-message.show {
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
}

/* Genre-specific Themes */
.theme-rock {
  --primary-color: #8b0000;
  --secondary-color: #dc143c;
  --accent-color: #ff4500;
  --background-color: #1a1a1a;
  --surface-color: #2d2d2d;
  --text-color: #ffffff;
  --text-light: #cccccc;
}

.theme-pop {
  --primary-color: #ff1493;
  --secondary-color: #ff69b4;
  --accent-color: #ffd700;
  --background-color: #fff0f5;
  --surface-color: #ffffff;
  --text-color: #333333;
  --text-light: #666666;
}

.theme-jazz {
  --primary-color: #191970;
  --secondary-color: #4169e1;
  --accent-color: #ffd700;
  --background-color: #f0f8ff;
  --surface-color: #ffffff;
  --text-color: #2f4f4f;
  --text-light: #708090;
}

.theme-electronic {
  --primary-color: #00ffff;
  --secondary-color: #ff00ff;
  --accent-color: #00ff00;
  --background-color: #0a0a0a;
  --surface-color: #1a1a1a;
  --text-color: #ffffff;
  --text-light: #cccccc;
}

.theme-classical {
  --primary-color: #4b0082;
  --secondary-color: #9370db;
  --accent-color: #daa520;
  --background-color: #f8f8ff;
  --surface-color: #ffffff;
  --text-color: #2f2f2f;
  --text-light: #696969;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-container {
    margin: 10px;
    border-radius: 10px;
  }

  .header {
    padding: 1rem;
  }

  .header .title {
    font-size: 1.5rem;
  }

  .genre-selector {
    padding: 1rem;
  }

  .genre-btn {
    margin: 0.1rem;
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
  }

  .player-section {
    padding: 1.5rem;
    margin: 0.5rem;
  }

  .album-art {
    width: 150px;
    height: 150px;
    font-size: 3rem;
  }

  .controls {
    gap: 0.5rem;
  }

  .control-btn {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }

  .play-btn {
    width: 70px;
    height: 70px;
    font-size: 1.8rem;
  }

  .playlist {
    margin: 0.5rem;
  }

  .track-item {
    padding: 0.75rem;
  }

  .volume-control {
    margin-top: 1.5rem;
  }
}

@media (max-width: 480px) {
  .app-container {
    margin: 5px;
    border-radius: 8px;
  }

  .header .level {
    flex-direction: column;
    gap: 1rem;
  }

  .genre-selector .buttons {
    flex-wrap: wrap;
    justify-content: center;
  }

  .controls {
    gap: 0.25rem;
  }

  .control-btn {
    width: 45px;
    height: 45px;
    font-size: 1rem;
  }

  .play-btn {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }

  .track-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .track-number {
    width: auto;
  }
}

/* Focus and Accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .control-btn,
  .genre-btn,
  .theme-toggle {
    border-width: 3px;
  }

  .progress-bar {
    border: 2px solid var(--text-color);
  }
}

/* Print styles */
@media print {
  .controls,
  .volume-control,
  .theme-toggle {
    display: none;
  }

  .app-container {
    box-shadow: none;
    margin: 0;
  }
}
